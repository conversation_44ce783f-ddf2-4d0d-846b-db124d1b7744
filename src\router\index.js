import { createRouter, createWebHistory } from 'vue-router'
import LoginRegister from '@/views/LoginRegister.vue'
import MainLayout from '@/views/MainLayout.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: LoginRegister,
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Home',
    component: MainLayout,
    meta: { requiresAuth: true }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: MainLayout,
    meta: { requiresAuth: true }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 使用 nextTick 确保 localStorage 操作已完成
  setTimeout(() => {
    const token = localStorage.getItem('token')
    const isAuthenticated = !!token

    console.log('路由守卫检查:', { to: to.path, token: !!token, requiresAuth: to.meta.requiresAuth })

    // 如果路由需要认证
    if (to.meta.requiresAuth) {
      if (isAuthenticated) {
        // 已登录，允许访问
        next()
      } else {
        // 未登录，重定向到登录页
        alert('请先登录')
        next('/login')
      }
    } else {
      // 不需要认证的路由
      if (to.path === '/login' && isAuthenticated) {
        // 已登录用户访问登录页，重定向到首页
        next('/')
      } else {
        next()
      }
    }
  }, 0)
})

export default router
