import axios from 'axios'
import router from '@/router'

const request = axios.create({
    baseURL: 'http://localhost:5000',
    timeout: 5000,
    withCredentials: true
})

request.interceptors.request.use(function (config) {
    const token = localStorage.getItem('token')
    
    if (token) {
        config.headers['Authorization'] = token
        config.headers['Content-Type'] = 'application/json'
    } else {
        config.headers['Content-Type'] = 'application/json'
        config.headers['Accept'] = 'application/json'
    }
    return config
}, function (error) {
    return Promise.reject(error)
})

// 添加响应拦截器来处理错误
request.interceptors.response.use(
    response => {
        return response
    },
    error => {
        if (error.response && error.response.status === 401) {
            // token无效或过期
            localStorage.removeItem('token')
            alert('登录已过期，请重新登录')
            router.push('/login')
        }
        console.error('请求错误:', error)
        return Promise.reject(error)
    }
)

export default request


